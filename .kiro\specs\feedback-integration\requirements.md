# 需求文档

## 介绍

本功能旨在将独立的"现场信息反馈小程序"集成到现有的"车辆维保小程序"中，实现两个功能模块的统一管理和用户状态共享。通过简单的按钮切换方式，用户可以在同一个小程序中访问车辆维保和现场信息反馈两个功能模块，避免重复登录和状态管理的复杂性。

## 需求

### 需求 1 - 首页功能切换界面

**用户故事：** 作为已登录的用户，我希望在车辆维保小程序首页看到功能切换按钮，以便我可以方便地在车辆维保和现场信息反馈功能之间切换。

#### 验收标准

1. WHEN 用户访问车辆维保小程序首页 THEN 系统 SHALL 在用户信息区域下方显示两个功能切换按钮
2. WHEN 用户点击"车辆维保"按钮 THEN 系统 SHALL 跳转到 pages/order/order 页面
3. WHEN 用户点击"现场信息反馈"按钮 THEN 系统 SHALL 跳转到 pages/fbindex/fbindex 页面
4. WHEN 显示功能切换按钮 THEN 系统 SHALL 使用微信原生button组件样式，保持界面简洁

### 需求 2 - 页面迁移和重命名

**用户故事：** 作为开发者，我需要将现场信息反馈小程序的主页面迁移到车辆维保小程序中，以便实现功能集成。

#### 验收标准

1. WHEN 执行页面迁移 THEN 系统 SHALL 将 feedback_management/pages/index 重命名为 feedback_management/pages/fbindex
2. WHEN 完成重命名 THEN 系统 SHALL 将 fbindex 页面及其相关组件复制到车辆维保小程序的 pages 目录
3. WHEN 复制页面文件 THEN 系统 SHALL 确保所有相关的 .wxml、.wxss、.js、.json 文件都被正确迁移
4. WHEN 迁移完成 THEN 系统 SHALL 更新所有页面内部的路径引用以适配新的目录结构

### 需求 3 - 统一用户认证系统

**用户故事：** 作为用户，我希望在车辆维保小程序中登录一次后，就能直接使用现场信息反馈功能，而不需要重复登录。

#### 验收标准

1. WHEN 用户访问 fbindex 页面 THEN 系统 SHALL 从 wx.getStorageSync("userInfo") 读取用户信息
2. WHEN 读取用户信息 THEN 系统 SHALL 获取 userId 或 PersonId 字段作为人员ID
3. WHEN 读取用户信息 THEN 系统 SHALL 获取 PersonName 或类似字段作为人员姓名
4. WHEN 读取用户信息 THEN 系统 SHALL 获取 Company 字段作为公司信息数组
5. IF 未找到有效用户信息 THEN 系统 SHALL 显示提示信息并引导用户返回首页登录
6. WHEN 集成完成 THEN 系统 SHALL 删除现场信息反馈小程序的独立登录模块

### 需求 4 - 应用配置更新

**用户故事：** 作为开发者，我需要更新车辆维保小程序的配置文件，以便支持新集成的现场信息反馈功能。

#### 验收标准

1. WHEN 更新配置 THEN 系统 SHALL 在车辆维保小程序的 app.json 中添加 fbindex 页面路径
2. WHEN 添加页面配置 THEN 系统 SHALL 添加现场信息反馈功能所需的权限配置（如位置权限）
3. WHEN 配置权限 THEN 系统 SHALL 复制必要的子包（subpackages）配置到车辆维保小程序
4. WHEN 配置完成 THEN 系统 SHALL 确保所有页面路径和权限配置正确无误

### 需求 5 - 依赖和工具类集成

**用户故事：** 作为开发者，我需要确保现场信息反馈功能的所有依赖都能在车辆维保小程序环境中正常工作。

#### 验收标准

1. WHEN 集成依赖 THEN 系统 SHALL 复制现场信息反馈小程序的工具类到车辆维保小程序的 utils 目录
2. WHEN 复制工具类 THEN 系统 SHALL 修改 fbindex 页面中的网络请求方法以适配车辆维保小程序环境
3. WHEN 处理组件依赖 THEN 系统 SHALL 确保 modal 等共用组件在新环境中正常工作
4. WHEN 更新引用路径 THEN 系统 SHALL 修改所有工具类和组件的引用路径以匹配新的目录结构
5. WHEN 集成完成 THEN 系统 SHALL 验证所有功能模块都能正常加载和运行

### 需求 6 - 功能验证和测试

**用户故事：** 作为用户，我希望集成后的功能能够稳定运行，两个模块之间的切换流畅无误。

#### 验收标准

1. WHEN 用户完成登录 THEN 系统 SHALL 在首页正确显示两个功能切换按钮
2. WHEN 用户点击功能切换按钮 THEN 系统 SHALL 正确跳转到对应的功能页面
3. WHEN 用户在 fbindex 页面操作 THEN 系统 SHALL 正确读取和使用共享的用户信息
4. WHEN 用户在两个功能模块间切换 THEN 系统 SHALL 保持用户登录状态不变
5. WHEN 执行现场信息反馈功能 THEN 系统 SHALL 确保所有原有功能（如位置获取、数据提交等）正常工作