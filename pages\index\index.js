// index.js
// 获取应用实例
const utils = require('../../utils/util');
const app = getApp()
const { userInfo, OpenId } = require('../../config/common')
const user = require('../../request/user')

Page({
    data: {
        userInfo: {},
    },
    radioChange(e) {
        let items = this.data.userInfo.Company;
        items.forEach(items => {
            if (items.CompanyId == e.detail.value) {
                items.IsMain = true;
            }
            else {
                items.IsMain = false;
            }
        })
        this.setData({
            'userInfo.Company': items
        })
        utils.setStorage(userInfo, this.data.userInfo)
    },
    AgainLogin() {
        wx.removeStorage({
            key: userInfo
        })
        this.getStologin();
    },

    // 跳转到车辆维保模块
    goToVehicleMaintenance() {
        wx.switchTab({
            url: '../order/order'
        });
    },

    // 跳转到现场信息反馈模块
    goToFeedback() {
        // 检查是否已登录
        console.log("跳转到现场信息反馈 - 当前用户信息:", this.data.userInfo);

        if (!this.data.userInfo || !this.data.userInfo.PersonId) {
            wx.showToast({
                title: '请先登录',
                icon: 'none'
            });
            return;
        }

        wx.navigateTo({
            url: '../fbindex/fbindex'
        });
    },
    //将登陆信息绑定到页面
    setUserInfo(res) {
        console.log("设置用户信息:", res);
        this.setData({
            userInfo: res
        })
    },
    checkSession() {
        //先检查登陆是否过期
        wx.checkSession({
            success: () => {
                /*没有过期的情况获取OpenId数据*/
                utils.getStorage('OpenId').then(
                    res => {
                         this.getStologin();
                    }
                ).catch(err => {
                    this.userLogin();
                })
            },
            fail: () => {
                wx.removeStorage({
                    key: 'OpenId'
                  })
                wx.removeStorage({
                  key: userInfo
                })
                /*过期了直接重新登陆*/
                this.userLogin();
            }
        })
    },
    //code换openid
    userLogin() {
        utils.wxLogin().then(
            res => {
                user.login(res);
            }
        )
    },
    getStologin() {
        utils.getStorage(userInfo).then(res => {

            this.setUserInfo(res);

        }).catch(err => {
            utils.getStorage('OpenId').then(res => {
                //有openid就跳转到登陆页
                wx.redirectTo({
                    url: '../Login/Login',
                })
            }).catch(err => {
                //无openId就重新获取openId
                console.log("没有openID");
            })
        })
    },
    onLoad() {
      
         this.checkSession()
        // this.getStologin();        
    }
})
