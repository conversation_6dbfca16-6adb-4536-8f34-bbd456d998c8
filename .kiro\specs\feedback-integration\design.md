# 设计文档

## 概述

本设计文档描述了将"现场信息反馈小程序"集成到"车辆维保小程序"中的技术实现方案。采用简单的功能模块切换方式，通过共享用户状态和统一配置管理，实现两个功能模块的无缝集成。

## 架构

### 整体架构图

```
车辆维保小程序
├── pages/
│   ├── index/           # 主页面（添加功能切换按钮）
│   ├── order/           # 车辆维保功能页面
│   ├── fbindex/         # 现场信息反馈主页面（新增）
│   └── ...              # 其他现有页面
├── components/
│   ├── modal/           # 共享模态框组件
│   └── ...              # 其他组件
├── utils/
│   ├── fbutils/         # 现场信息反馈工具类（新增）
│   └── ...              # 其他工具类
└── app.json             # 应用配置（更新）
```

### 数据流架构

```
用户登录 → 存储到 wx.storage("userInfo") → 两个功能模块共享用户状态
```

## 组件和接口

### 1. 首页功能切换组件

**位置**: `pages/index/index.wxml`

**组件结构**:
```xml
<!-- 用户信息区域 -->
<view class="user-info">
  <!-- 现有用户信息显示 -->
</view>

<!-- 功能切换按钮区域 -->
<view class="function-switch">
  <button type="primary" bindtap="goToOrder">车辆维保</button>
  <button type="primary" bindtap="goToFeedback">现场信息反馈</button>
</view>
```

**接口方法**:
- `goToOrder()`: 跳转到车辆维保页面
- `goToFeedback()`: 跳转到现场信息反馈页面

### 2. 用户状态管理接口

**存储结构**:
```javascript
// wx.getStorageSync("userInfo") 数据结构
{
  userId: String,        // 或 PersonId
  PersonName: String,    // 人员姓名
  Company: Array,        // 公司信息数组
  // 其他用户信息字段...
}
```

**接口方法**:
- `getUserInfo()`: 从本地存储获取用户信息
- `validateUserInfo()`: 验证用户信息有效性
- `redirectToLogin()`: 引导用户返回登录页面

### 3. 页面迁移接口

**源路径**: `feedback_management/pages/index/`
**目标路径**: `pages/fbindex/`

**迁移文件**:
- `index.wxml` → `fbindex.wxml`
- `index.wxss` → `fbindex.wxss`
- `index.js` → `fbindex.js`
- `index.json` → `fbindex.json`

## 数据模型

### 用户信息模型

```javascript
class UserInfo {
  constructor(data) {
    this.userId = data.userId || data.PersonId;
    this.personName = data.PersonName;
    this.company = data.Company || [];
  }
  
  isValid() {
    return this.userId && this.personName;
  }
}
```

### 页面配置模型

```javascript
// app.json 页面配置
{
  "pages": [
    "pages/index/index",
    "pages/order/order",
    "pages/fbindex/fbindex",  // 新增
    // 其他页面...
  ],
  "permission": {
    "scope.userLocation": {
      "desc": "现场信息反馈功能需要获取您的位置信息"
    }
  }
}
```

## 错误处理

### 1. 用户信息缺失处理

```javascript
// 在 fbindex 页面的 onLoad 方法中
onLoad() {
  const userInfo = wx.getStorageSync("userInfo");
  if (!userInfo || !userInfo.userId) {
    wx.showModal({
      title: '提示',
      content: '请先登录后再使用此功能',
      showCancel: false,
      success: () => {
        wx.navigateBack();
      }
    });
    return;
  }
  // 继续页面初始化...
}
```

### 2. 页面跳转错误处理

```javascript
// 首页跳转方法
goToFeedback() {
  wx.navigateTo({
    url: '/pages/fbindex/fbindex',
    fail: (err) => {
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
      console.error('跳转失败:', err);
    }
  });
}
```

### 3. 工具类引用错误处理

```javascript
// 在 fbindex.js 中安全引用工具类
try {
  const fbUtils = require('../../utils/fbutils/index.js');
  // 使用工具类...
} catch (error) {
  console.error('工具类加载失败:', error);
  // 降级处理或显示错误提示
}
```

## 测试策略

### 1. 单元测试

**测试范围**:
- 用户信息验证逻辑
- 页面跳转方法
- 工具类方法

**测试用例**:
```javascript
// 用户信息验证测试
describe('UserInfo Validation', () => {
  test('should return true for valid user info', () => {
    const userInfo = new UserInfo({
      userId: '123',
      PersonName: '张三',
      Company: ['公司A']
    });
    expect(userInfo.isValid()).toBe(true);
  });
  
  test('should return false for invalid user info', () => {
    const userInfo = new UserInfo({});
    expect(userInfo.isValid()).toBe(false);
  });
});
```

### 2. 集成测试

**测试场景**:
1. 用户登录后首页显示功能切换按钮
2. 点击按钮正确跳转到对应页面
3. fbindex 页面正确读取用户信息
4. 两个功能模块间切换保持用户状态

### 3. 用户验收测试

**测试流程**:
1. 用户登录车辆维保小程序
2. 在首页查看功能切换按钮
3. 点击"现场信息反馈"按钮
4. 验证页面跳转和功能正常
5. 返回首页，点击"车辆维保"按钮
6. 验证功能切换流畅

## 实施计划

### 阶段1: 页面迁移和重命名
- 重命名 feedback_management/pages/index 为 fbindex
- 复制页面文件到车辆维保小程序
- 更新页面内部路径引用

### 阶段2: 配置文件更新
- 更新 app.json 添加 fbindex 页面
- 添加位置权限配置
- 复制必要的子包配置

### 阶段3: 用户认证集成
- 修改 fbindex 页面的用户验证逻辑
- 实现从共享存储读取用户信息
- 删除独立登录模块

### 阶段4: 首页功能切换
- 在首页添加功能切换按钮
- 实现页面跳转逻辑
- 优化按钮样式和布局

### 阶段5: 依赖处理和测试
- 复制和适配工具类
- 确保组件正常工作
- 进行全面测试和调试

## 风险和缓解措施

### 风险1: 用户信息字段不匹配
**缓解措施**: 实现兼容性检查，支持多种字段名称映射

### 风险2: 页面路径引用错误
**缓解措施**: 使用相对路径，建立统一的路径管理机制

### 风险3: 工具类依赖冲突
**缓解措施**: 使用命名空间隔离，避免全局变量冲突

### 风险4: 权限配置不完整
**缓解措施**: 详细检查原有小程序的权限需求，确保完整迁移