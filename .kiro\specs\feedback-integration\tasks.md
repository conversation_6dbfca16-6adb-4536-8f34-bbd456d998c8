# 实施计划

- [x] 1. 页面迁移和重命名










  - 将 feedback_management/pages/index 目录重命名为 feedback_management/pages/fbindex
  - 复制 fbindex 页面的所有文件（.wxml, .wxss, .js, .json）到车辆维保小程序的 pages 目录
  - 更新 fbindex.js 中的页面路径引用和组件引用路径
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [ ] 2. 应用配置文件更新
  - 在车辆维保小程序的 app.json 中添加 "pages/fbindex/fbindex" 页面路径
  - 添加位置权限配置到 app.json 的 permission 字段
  - 复制现场信息反馈小程序的子包配置到车辆维保小程序的 app.json
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 3. 工具类和依赖迁移
  - 复制 feedback_management/utils 目录下的工具类到车辆维保小程序的 utils/fbutils 目录
  - 更新 fbindex 页面中工具类的引用路径
  - 确保 modal 等共用组件在新环境中正常工作
  - _需求: 5.1, 5.2, 5.3, 5.4_

- [ ] 4. 用户认证系统集成
  - 修改 fbindex.js 的 onLoad 方法，实现从 wx.getStorageSync("userInfo") 读取用户信息
  - 实现用户信息验证逻辑，支持 userId/PersonId、PersonName、Company 字段读取
  - 添加用户信息缺失时的错误处理和引导逻辑
  - 删除 fbindex 页面中的独立登录相关代码
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [ ] 5. 首页功能切换按钮实现
  - 在 pages/index/index.wxml 的用户信息区域下方添加功能切换按钮容器
  - 添加"车辆维保"和"现场信息反馈"两个微信原生 button 组件
  - 在 pages/index/index.js 中实现 goToOrder 和 goToFeedback 跳转方法
  - 添加页面跳转的错误处理逻辑
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [ ] 6. 样式和布局优化
  - 在 pages/index/index.wxss 中添加功能切换按钮的样式
  - 确保按钮布局在不同屏幕尺寸下的适配性
  - 保持微信原生按钮样式的简洁性
  - _需求: 1.4_

- [ ] 7. 网络请求适配
  - 检查 fbindex 页面中的网络请求方法
  - 修改请求基础URL和配置以适配车辆维保小程序环境
  - 确保API接口调用正常工作
  - _需求: 5.2_

- [ ] 8. 功能测试和验证
  - 测试用户登录后首页功能切换按钮的显示
  - 测试点击按钮的页面跳转功能
  - 测试 fbindex 页面的用户信息读取和验证
  - 测试现场信息反馈功能的完整流程
  - 验证两个功能模块间的切换流畅性
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 9. 错误处理完善
  - 实现用户信息缺失时的友好提示
  - 添加页面跳转失败的错误处理
  - 实现工具类加载失败的降级处理
  - 添加网络请求异常的错误提示
  - _需求: 3.5, 5.5_

- [ ] 10. 代码清理和优化
  - 删除不再需要的独立登录模块代码
  - 清理无用的文件和依赖
  - 优化代码结构和注释
  - 确保代码符合项目规范
  - _需求: 3.6, 5.5_